package exchange.app.config;

import exchange.app.model.UserLog;
import exchange.common.core.tracer.TraceIdHolder;
import exchange.common.util.JsonUtil;
import java.io.IOException;
import java.util.Arrays;
import java.util.Date;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import com.fasterxml.jackson.databind.ObjectMapper;
import exchange.common.constant.ErrorCode;
import exchange.common.entity.User;
import exchange.common.model.response.ErrorData;
import exchange.common.service.LogService;
import exchange.common.service.UserService;
import lombok.RequiredArgsConstructor;

@Slf4j
@Component
@RequiredArgsConstructor
public class HandlerInterceptorImpl implements HandlerInterceptor {

  public static final String START_DURATION = "startDuration";
  public static final String ANONYMOUS_USER = "anonymousUser";

  private final LogService logService;
  private final UserService userService;

  @Override
  public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
      throws Exception {
    TraceIdHolder.genAndSetTraceId();
    request.setAttribute(START_DURATION, new Date().getTime());
    logService.logRequestStart(request);

    // check user status
    if (!ANONYMOUS_USER.equals(
        SecurityContextHolder.getContext().getAuthentication().getPrincipal())) {
      User userInSession =
          (User) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
      User user = userService.findOne(userInSession.getId());
      log.info("[tid={}] request userEmail: {}, userId is: {}", TraceIdHolder.getTraceIdOrUnknown(), user.getEmail(), user.getId());
      if (!user.isAllowedToLogin()) {
        request.logout(); // 強制ログアウト
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response
            .getWriter()
            .write(
                new ObjectMapper()
                    .writeValueAsString(
                        new ErrorData(ErrorCode.REQUEST_ERROR_INVALID_USER_STATUS)));
        return false;
      }
    }

    return true;
  }

  @Override
  public void postHandle(
      HttpServletRequest request,
      HttpServletResponse response,
      Object handler,
      ModelAndView modelAndView) {}

  @Override
  public void afterCompletion(
      HttpServletRequest request, HttpServletResponse response, Object handler, Exception e)
      throws IOException {
    final var requestUri = request.getRequestURI();
    // whiteListのPathはログを出力しない
    final var isWhiteListContain = Arrays.asList(WebSecurityConfig.whiteListEntryPoint).contains(requestUri);
    if (requestUri.startsWith("/app/") && !isWhiteListContain) {
      log.info(JsonUtil.encode(new UserLog(request, response)));
    }
    log.info(
        "*** end [tid=" + TraceIdHolder.getTraceIdOrUnknown() + "] "
            + request.getRequestURL().toString()
            + " "
            + (new Date().getTime() - (long) request.getAttribute(START_DURATION))
            + " ***");
    TraceIdHolder.clear();
  }
}
