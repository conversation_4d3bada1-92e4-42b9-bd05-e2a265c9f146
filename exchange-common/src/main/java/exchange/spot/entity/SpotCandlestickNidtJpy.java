package exchange.spot.entity;

import exchange.common.entity.Candlestick;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Entity
@NoArgsConstructor
@Table(name = "spot_candlestick_nidt_jpy")
@ToString(callSuper = true, doNotUseGetters = true)
public class SpotCandlestickNidtJpy extends Candlestick {

  private static final long serialVersionUID = 8527418927514667259L;
}