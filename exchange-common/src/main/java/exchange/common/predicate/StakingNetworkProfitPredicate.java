package exchange.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import exchange.common.constant.Currency;
import exchange.common.entity.StakingNetworkProfit;
import exchange.common.entity.StakingNetworkProfit_;

@Component
public class StakingNetworkProfitPredicate extends EntityPredicate<StakingNetworkProfit> {

  public Predicate equalCurrency(
      CriteriaBuilder criteriaBuilder, Root<StakingNetworkProfit> root, Currency currency) {
    return criteriaBuilder.equal(root.get(StakingNetworkProfit_.currency), currency);
  }
  
}
