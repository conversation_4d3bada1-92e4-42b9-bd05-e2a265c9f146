package exchange.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import exchange.common.constant.KycStatus;
import exchange.common.constant.UserStatus;
import exchange.common.constant.AntisocialStatus;
import exchange.common.entity.User;
import exchange.common.entity.UserInfoCorporate_;
import exchange.common.entity.UserInfo_;
import exchange.common.entity.UserKyc_;
import exchange.common.entity.User_;

@Component
public class UserPredicate extends EntityPredicate<User> {

  public Predicate equalEmail(CriteriaBuilder criteriaBuilder, Root<User> root, String email) {
    return criteriaBuilder.equal(root.get(User_.email), email);
  }

  public Predicate likeEmail(CriteriaBuilder criteriaBuilder, Root<User> root, String email) {
    return criteriaBuilder.like(root.get(User_.email), "%" + email + "%");
  }

  public Predicate likeFirstName(
      CriteriaBuilder criteriaBuilder, Root<User> root, String firstName) {
    return criteriaBuilder.like(
        root.get(User_.userInfo).get(UserInfo_.firstName), "%" + firstName + "%");
  }

  public Predicate likeLastName(CriteriaBuilder criteriaBuilder, Root<User> root, String lastName) {
    return criteriaBuilder.like(
        root.get(User_.userInfo).get(UserInfo_.lastName), "%" + lastName + "%");
  }

  public Predicate likeFirstKana(
      CriteriaBuilder criteriaBuilder, Root<User> root, String firstKana) {
    return criteriaBuilder.like(
        root.get(User_.userInfo).get(UserInfo_.firstKana), "%" + firstKana + "%");
  }

  public Predicate likeLastKana(CriteriaBuilder criteriaBuilder, Root<User> root, String lastKana) {
    return criteriaBuilder.like(
        root.get(User_.userInfo).get(UserInfo_.lastKana), "%" + lastKana + "%");
  }

  public Predicate likeCorporateName(
      CriteriaBuilder criteriaBuilder, Root<User> root, String corporateName) {
    return criteriaBuilder.like(
        root.get(User_.userInfoCorporate).get(UserInfoCorporate_.name), "%" + corporateName + "%");
  }

  public Predicate likeCorporateKana(
      CriteriaBuilder criteriaBuilder, Root<User> root, String corporateKana) {
    return criteriaBuilder.like(
        root.get(User_.userInfoCorporate).get(UserInfoCorporate_.nameKana),
        "%" + corporateKana + "%");
  }

  public Predicate equalUserStatus(
      CriteriaBuilder criteriaBuilder, Root<User> root, UserStatus userStatus) {
    return criteriaBuilder.equal(root.get(User_.userStatus), userStatus);
  }

  public Predicate notEqualUserStatus(
      CriteriaBuilder criteriaBuilder, Root<User> root, UserStatus userStatus) {
    return criteriaBuilder.notEqual(root.get(User_.userStatus), userStatus);
  }

  public Predicate equalKycStatus(
      CriteriaBuilder criteriaBuilder, Root<User> root, KycStatus kycStatus) {
    return criteriaBuilder.equal(root.get(User_.kycStatus), kycStatus);
  }

  public Predicate equalUserKycId(
      CriteriaBuilder criteriaBuilder, Root<User> root, Long userKycId) {
    return criteriaBuilder.equal(root.get(User_.USER_KYC_ID), userKycId);
  }

  public Predicate equalEnabled(CriteriaBuilder criteriaBuilder, Root<User> root, boolean enabled) {
    return criteriaBuilder.equal(root.get(User_.enabled), enabled);
  }

  public Predicate isTradeUncapped(
      CriteriaBuilder criteriaBuilder, Root<User> root, boolean isTradeUncapped) {
    return criteriaBuilder.equal(root.get(User_.tradeUncapped), isTradeUncapped);
  }
  
  public Predicate isInsideAccountFlg(
	      CriteriaBuilder criteriaBuilder, Root<User> root, boolean isInsideAccountFlg) {
	    return criteriaBuilder.equal(root.get(User_.insideAccountFlg), isInsideAccountFlg);
	  }
  
  public Predicate inKycStatus(CriteriaBuilder criteriaBuilder, Root<User> root, KycStatus[] kycStatus) {
    return root.get(User_.kycStatus).in((Object[]) kycStatus);
  }
  
  public Predicate equalAffiliateInfoId(
      CriteriaBuilder criteriaBuilder, Root<User> root, Long affiliateInfoId) {
    return criteriaBuilder.equal(root.get(User_.affiliateInfoId), affiliateInfoId);
  }
  
  public Predicate equalUuid(
      CriteriaBuilder criteriaBuilder, Root<User> root, String uuid) {
    return criteriaBuilder.equal(root.get(User_.uuid), uuid);
  }

  public Predicate equalAntisocialStatus(
          CriteriaBuilder criteriaBuilder, Root<User> root, AntisocialStatus antisocialStatus) {
    return criteriaBuilder.equal(
            root.get(User_.userKyc).get(UserKyc_.antisocialStatus),
              antisocialStatus);
  }

  public Predicate equalCaseId(CriteriaBuilder criteriaBuilder, Root<User> root, Long caseId) {
    return criteriaBuilder.equal(root.get(User_.caseId), caseId);
  }
  public Predicate notInKycStatus(CriteriaBuilder criteriaBuilder, Root<User> root, KycStatus[] kycStatus) {
    return root.get(User_.kycStatus).in((Object[]) kycStatus).not();
  }

  public Predicate notInUserStatus(CriteriaBuilder criteriaBuilder, Root<User> root, UserStatus[] userStatus) {
    return root.get(User_.userStatus).in((Object[]) userStatus).not();
  }
}
