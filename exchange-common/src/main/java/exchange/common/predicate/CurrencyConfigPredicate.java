package exchange.common.predicate;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Component;
import exchange.common.constant.Currency;
import exchange.common.entity.CurrencyConfig;
import exchange.common.entity.CurrencyConfig_;

@Component
public class CurrencyConfigPredicate extends EntityPredicate<CurrencyConfig> {

  public Predicate equalCurrency(
      CriteriaBuilder criteriaBuilder, Root<CurrencyConfig> root, Currency currency) {
    return criteriaBuilder.equal(root.get(CurrencyConfig_.currency), currency);
  }

  public Predicate isEnabled(
      CriteriaBuilder criteriaBuilder, Root<CurrencyConfig> root, boolean enabled) {
    return enabled
        ? criteriaBuilder.isTrue(root.get(CurrencyConfig_.enabled))
        : criteriaBuilder.isFalse(root.get(CurrencyConfig_.enabled));
  }
  
  public Predicate isFirstSet(
      CriteriaBuilder criteriaBuilder, Root<CurrencyConfig> root, boolean firstSetFlg) {
    return firstSetFlg
        ? criteriaBuilder.isTrue(root.get(CurrencyConfig_.firstSetFlg))
        : criteriaBuilder.isFalse(root.get(CurrencyConfig_.firstSetFlg));
  }
}
