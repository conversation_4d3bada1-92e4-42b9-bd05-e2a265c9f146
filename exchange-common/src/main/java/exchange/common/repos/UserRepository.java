package exchange.common.repos;

import exchange.common.constant.KycStatus;
import exchange.common.entity.User;
import exchange.common.entity.UserEkyc;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface UserRepository extends JpaRepository<User, Long>, JpaSpecificationExecutor<User> {

  List<User> findByKycStatusAndUserInfoIdNotNull(KycStatus kycStatus);

  List<User> findByKycStatusInOrderByIdDesc(List<KycStatus> status);

  List<User> findByIdIn(List<Long> id);
}
