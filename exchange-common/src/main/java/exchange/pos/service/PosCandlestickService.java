package exchange.pos.service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import exchange.common.component.QueryExecutorReturner;
import exchange.common.constant.CandlestickType;
import exchange.common.entity.Candlestick_;
import exchange.common.entity.Symbol;
import exchange.common.service.EntityService;
import exchange.common.util.JsonUtil;
import exchange.pos.entity.PosCandlestick;
import exchange.pos.entity.PosTrade;
import exchange.pos.predicate.PosCandlestickPredicate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class PosCandlestickService extends EntityService<PosCandlestick, PosCandlestickPredicate> {

  private final PosTradeService posTradeService;

  @Override
  public Class<PosCandlestick> getEntityClass() {
    return PosCandlestick.class;
  }

  private String getCacheKey(Long symbolId, CandlestickType candlestickType, Date targetAt) {
    return "candlestick:" + symbolId + ":" + candlestickType + ":" + targetAt.getTime();
  }

  @Override
  protected void saveCache(PosCandlestick candlestick) {
    super.saveCache(candlestick);
    redisTemplate.setValue(getCacheKey(candlestick.getSymbolId()), candlestick);
  }

  @Override
  protected void deleteCache(PosCandlestick candlestick) {
    super.deleteCache(candlestick);
    redisTemplate.delete(getCacheKey(candlestick.getSymbolId()));
  }
  protected List<Predicate> getIndexedPredicates(CriteriaBuilder criteriaBuilder,
      Root<PosCandlestick> root, Long symbolId, CandlestickType candlestickType) {
    List<Predicate> predicates = new ArrayList<>();
    predicates.add(predicate.equalSymbolId(criteriaBuilder, root, symbolId));
    predicates.add(predicate.equalCandlestickType(criteriaBuilder, root, candlestickType));
    return predicates;
  }

  public PosCandlestick findOne(Long symbolId, CandlestickType candlestickType, Date targetAt) {
    PosCandlestick candlestick = null;

    try {
      candlestick = redisTemplate.getValue(getCacheKey(symbolId, candlestickType, targetAt));
    } catch (Exception e) {
      // do nothing
    }

    if (candlestick == null) {
      candlestick =
          customTransactionManager.find(
              getEntityClass(),
              new QueryExecutorReturner<PosCandlestick, PosCandlestick>() {
                @Override
                public PosCandlestick query() {
                  List<Predicate> predicates =
                      getIndexedPredicates(criteriaBuilder, root, symbolId, candlestickType);
                  predicates.add(predicate.equalTargetAt(criteriaBuilder, root, targetAt));
                  return getSingleResult(entityManager, criteriaQuery, root, predicates);
                }
              });

      if (candlestick != null) {
        saveCache(candlestick);
      }
    }

    return candlestick;
  }

  public PosCandlestick findOrCreate(Long symbolId, CandlestickType candlestickType,
      Date targetAt) {
    PosCandlestick candlestick = findOne(symbolId, candlestickType, targetAt);

    if (candlestick == null) {
      candlestick = newEntity();
      candlestick.setProperties(symbolId, candlestickType, targetAt);
    }

    return candlestick;
  }

  public PosCandlestick findLatest(Long symbolId, CandlestickType candlestickType, Date targetAt) {
    return customTransactionManager.find(getEntityClass(),
        new QueryExecutorReturner<PosCandlestick, PosCandlestick>() {
          @Override
          public PosCandlestick query() {
            List<Predicate> predicates = getIndexedPredicates(criteriaBuilder, root, symbolId,
                candlestickType);
            predicates.add(predicate.lessThanTargetAt(criteriaBuilder, root, targetAt));
            return getSingleResult(entityManager, criteriaQuery, root, predicates,
                criteriaBuilder.desc(root.get(Candlestick_.targetAt)));
          }
        });
  }

  public List<PosCandlestick> findByCondition(
      Long symbolId, CandlestickType candlestickType, Date dateFrom, Date dateTo) {
    // 使用自定义的事务管理器查找满足条件的PosCandlestick列表
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<PosCandlestick, List<PosCandlestick>>() {
          @Override
          public List<PosCandlestick> query() {
            // 获取满足条件的谓词列表
            List<Predicate> predicates =
                getIndexedPredicates(criteriaBuilder, root, symbolId, candlestickType);

            // 如果dateFrom不为空，则添加大于等于dateFrom的谓词
            if (dateFrom != null) {
              predicates.add(
                  predicate.greaterThanOrEqualToTargetAt(criteriaBuilder, root, dateFrom));
            }

            // 如果dateTo不为空，则添加小于dateTo的谓词
            if (dateTo != null) {
              predicates.add(predicate.lessThanTargetAt(criteriaBuilder, root, dateTo));
            }

            // 返回满足条件的PosCandlestick列表
            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                0,
                Integer.MAX_VALUE,
                criteriaBuilder.asc(root.get(Candlestick_.targetAt)));
          }
        });
  }

  public List<PosCandlestick> findByCondition(
      Long symbolId, CandlestickType candlestickType, Date dateFrom, Date dateTo, Integer num) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<PosCandlestick, List<PosCandlestick>>() {
          @Override
          public List<PosCandlestick> query() {
            List<Predicate> predicates =
                getIndexedPredicates(criteriaBuilder, root, symbolId, candlestickType);

            if (dateFrom != null) {
              predicates.add(
                  predicate.greaterThanOrEqualToTargetAt(criteriaBuilder, root, dateFrom));
            }

            if (dateTo != null) {
              predicates.add(predicate.lessThanTargetAt(criteriaBuilder, root, dateTo));
            }

            return getResultList(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                0,
                num,
                criteriaBuilder.desc(root.get(Candlestick_.targetAt)));
          }
        });
  }


  public void make(Symbol symbol, CandlestickType candlestickType, Date targetAt) {
    targetAt = candlestickType.getTargetAt(targetAt);
    PosCandlestick previous = findLatest(symbol.getId(), candlestickType, targetAt);
    log.info("maker date: {} posCandlestick: {}",targetAt ,JsonUtil.encode(previous));
    if (previous != null && !previous.isFixed()) {
      PosCandlestick morePrevious =
          findPrevious(
              previous.getSymbolId(), previous.getCandlestickType(), previous.getTargetAt());

      if (candlestickType == CandlestickType.PT1M) {
        makeMinute1(symbol, previous, morePrevious);
      } else {
        makeWithoutMinute1(symbol, previous, morePrevious);
      }
    }
    PosCandlestick candlestick = findOrCreate(symbol.getId(), candlestickType, targetAt);

    if (candlestickType == CandlestickType.PT1M) {
      makeMinute1(symbol, candlestick, previous);
    } else {
      makeWithoutMinute1(symbol, candlestick, previous);
    }

  }

  private PosCandlestick findPrevious(Long symbolId, CandlestickType candlestickType,
      Date targetAt) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<PosCandlestick, PosCandlestick>() {
          @Override
          public PosCandlestick query() {
            List<Predicate> predicates =
                getIndexedPredicates(criteriaBuilder, root, symbolId, candlestickType);
            predicates.add(predicate.lessThanTargetAt(criteriaBuilder, root, targetAt));
            return getSingleResult(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                criteriaBuilder.desc(root.get(Candlestick_.targetAt)));
          }
        });
  }

  private void makeMinute1(Symbol symbol, PosCandlestick candlestick, PosCandlestick previous) {
    log.info("makeMinute1 fromTargetAt:{},toTargetAt:{}",candlestick.getTargetAt(),candlestick.getCandlestickType().getNextTargetAt(candlestick.getTargetAt()));
    // Takerには販売所のスプレッドが含まれるため、Makerで生成する
    List<PosTrade> posTrades =
        posTradeService
            .findMakerByCondition(
                symbol.getId(),
                candlestick.getTargetAt(),
                candlestick.getCandlestickType().getNextTargetAt(candlestick.getTargetAt()));
    candlestick.reset();

    if (CollectionUtils.isEmpty(posTrades)) {
      updateByPrevious(candlestick, previous);
    } else {
      posTrades.forEach(
          cxrTradeData ->
              candlestick.update(
                  cxrTradeData.getPrice(),
                  cxrTradeData.getPrice(),
                  cxrTradeData.getPrice(),
                  cxrTradeData.getPrice(),
                  cxrTradeData.getAmount()));
    }

    if (new Date()
        .after(candlestick.getCandlestickType().getNextTargetAt(candlestick.getTargetAt()))) {
      candlestick.setFixed(true);
    }

    save(candlestick);
  }

  protected void updateByPrevious(PosCandlestick candlestick, PosCandlestick previous) {
    if (previous == null) {
      return;
    }

    candlestick.update(
        previous.getClose(),
        previous.getClose(),
        previous.getClose(),
        previous.getClose(),
        BigDecimal.ZERO);
  }

  private void makeWithoutMinute1(Symbol symbol, PosCandlestick candlestick,
      PosCandlestick previous) {
    Date nextTargetAt = candlestick.getCandlestickType().getNextTargetAt(candlestick.getTargetAt());
    List<PosCandlestick> elements =
        findByCondition(
            candlestick.getSymbolId(),
            candlestick.getCandlestickType().getElementType(),
            candlestick.getTargetAt(),
            nextTargetAt);
    candlestick.reset();

    if (CollectionUtils.isEmpty(elements)) {
      updateByPrevious(candlestick, previous);
    } else {
      elements
          .stream()
          .forEach(
              element ->
                  candlestick.update(
                      element.getOpen(),
                      element.getHigh(),
                      element.getLow(),
                      element.getClose(),
                      element.getVolume()));
    }

    if (new Date().after(nextTargetAt)) {
      candlestick.setFixed(true);
    }

    save(candlestick);
  }
  
  public PosCandlestick findOneByCondition(
	      Long symbolId,
	      CandlestickType candlestickType,
	      Long dateFrom,
	      Long dateTo,
	      boolean isAscending, boolean isEnable) {
	    return customTransactionManager.find(
	        getEntityClass(),
	        new QueryExecutorReturner<PosCandlestick, PosCandlestick>() {
	          @Override
	          public PosCandlestick query() {
	            List<Predicate> predicates =
	                getIndexedPredicates(criteriaBuilder, root, symbolId, candlestickType);

	            if (dateFrom != null) {
	              predicates.add(
	                  predicate.greaterThanOrEqualToTargetAt(
	                      criteriaBuilder, root, new Date(dateFrom)));
	            }

	            if (dateTo != null) {
	              predicates.add(predicate.lessThanTargetAt(criteriaBuilder, root, new Date(dateTo)));
	            }
	            
	            if (!isEnable) {
	              // 無効な場合、OkCoinから取得した終値（open=0）を取得
                  predicates.add(predicate.equalOpen(criteriaBuilder, root, new BigDecimal(0)));
	            }

	            return getSingleResult(
	                entityManager,
	                criteriaQuery,
	                root,
	                predicates,
	                isAscending
	                    ? criteriaBuilder.asc(root.get(Candlestick_.targetAt))
	                    : criteriaBuilder.desc(root.get(Candlestick_.targetAt)));
	          }
	        });
  }
  
  public PosCandlestick findOneByConditionForJpyConversion(
      Long symbolId,
      CandlestickType candlestickType,
      Long dateFrom,
      Long dateTo,
      boolean isAscending) {
    return customTransactionManager.find(
        getEntityClass(),
        new QueryExecutorReturner<PosCandlestick, PosCandlestick>() {
          @Override
          public PosCandlestick query() {
            List<Predicate> predicates =
                getIndexedPredicates(criteriaBuilder, root, symbolId, candlestickType);

            if (dateFrom != null) {
              predicates.add(
                  predicate.greaterThanOrEqualToUpdatedAt(
                      criteriaBuilder, root, new Date(dateFrom)));
            }

            if (dateTo != null) {
              predicates.add(predicate.lessThanUpdatedAt(criteriaBuilder, root, new Date(dateTo)));
            }

            // OkCoinから取得した終値
            predicates.add(predicate.equalOpen(criteriaBuilder, root, new BigDecimal(0)));
            return getSingleResult(
                entityManager,
                criteriaQuery,
                root,
                predicates,
                isAscending
                    ? criteriaBuilder.asc(root.get(Candlestick_.targetAt))
                    : criteriaBuilder.desc(root.get(Candlestick_.targetAt)));
          }
        });
}

  @Override
  public void redisPublish(PosCandlestick entity){
    redisPublisher.publish(entity);
  }
}
