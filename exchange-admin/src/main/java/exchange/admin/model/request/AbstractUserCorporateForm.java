package exchange.admin.model.request;

import javax.validation.constraints.AssertTrue;
import com.fasterxml.jackson.annotation.JsonIgnore;
import exchange.common.model.request.IdForm;
import exchange.common.model.request.UserInfoCorporateForm;
import exchange.common.model.request.UserInfoCorporateForm.Owner;
import lombok.Getter;
import lombok.Setter;

public abstract class AbstractUserCorporateForm extends IdForm {

  @Getter @Setter private UserInfoCorporateForm userInfoCorporate;

  @JsonIgnore
  @AssertTrue(message = "法人名を255文字以内で入力してください。")
  public boolean isValidName() {
    return userInfoCorporate.validateName(false);
  }

  @JsonIgnore
  @AssertTrue(message = "法人名（カナ）を255文字以内で入力してください。")
  public boolean isValidNameKana() {
    return userInfoCorporate.validateNameKana(false);
  }

  @JsonIgnore
  @AssertTrue(message = "設立年を4文字以内の数値で入力してください。")
  public boolean isValidEstablishedYear() {
    return userInfoCorporate.validateEstablishedYear(false);
  }

  @JsonIgnore
  @AssertTrue(message = "設立月を入力してください。")
  public boolean isValidEstablishedMonth() {
    return userInfoCorporate.validateEstablishedMonth(false);
  }

  @JsonIgnore
  @AssertTrue(message = "設立日を入力してください。")
  public boolean isValidEstablishedDay() {
    return userInfoCorporate.validateEstablishedDay(false);
  }

  @JsonIgnore
  @AssertTrue(message = "決算月を入力してください。")
  public boolean isValidAccountingMonth() {
    return userInfoCorporate.validateAccountingMonth(false);
  }

  @JsonIgnore
  @AssertTrue(message = "郵便番号を7桁の数値で入力してください。")
  public boolean validateZipCode() {
    return userInfoCorporate.validateZipCode(false);
  }

  @JsonIgnore
  @AssertTrue(message = "都道府県を入力してください。")
  public boolean isValidPrefecture() {
    return userInfoCorporate.validatePrefecture(false);
  }

  @JsonIgnore
  @AssertTrue(message = "市区町村を255文字以内で入力してください。")
  public boolean isValidCity() {
    return userInfoCorporate.validateCity(false);
  }

  @JsonIgnore
  @AssertTrue(message = "町名番地を255文字以内で入力してください。")
  public boolean isValidAddress() {
    return userInfoCorporate.validateAddress(false);
  }

  @JsonIgnore
  @AssertTrue(message = "建物名を255文字以内で入力してください。")
  public boolean isValidBuilding() {
    return userInfoCorporate.validateBuilding(true);
  }

  @JsonIgnore
  @AssertTrue(message = "電話番号は11桁以内の数値で、1文字目は0を入力してください。")
  public boolean isValidPhoneNumber() {
    return userInfoCorporate.validatePhoneNumber(false);
  }

  @JsonIgnore
  @AssertTrue(message = "事業内容を255文字以内で入力してください。")
  public boolean isValidBusinessContent() {
    return userInfoCorporate.validateBusinessContent(false);
  }

  @JsonIgnore
  @AssertTrue(message = "年商を選択してください。")
  public boolean isValidSales() {
    return userInfoCorporate.validateSales(false);
  }

  @JsonIgnore
  @AssertTrue(message = "金融資産を選択してください。")
  public boolean isValidFinancialAssets() {
    return userInfoCorporate.validateFinancialAssets(false);
  }

  @JsonIgnore
  @AssertTrue(message = "主なご利用目的を選択してください。")
  public boolean isValidPurpose() {
    return userInfoCorporate.validatePurpose(false);
  }

  @JsonIgnore
  @AssertTrue(message = "投資目的を選択してください。")
  public boolean isValidInvestmentPurposes() {
    return userInfoCorporate.validateInvestmentPurposes(false);
  }

  @JsonIgnore
  @AssertTrue(message = "投資経験（暗号資産）を選択してください。")
  public boolean isValidCryptoExperience() {
    return userInfoCorporate.validateCryptoExperience(false);
  }

  @JsonIgnore
  @AssertTrue(message = "投資経験（FX）を選択してください。")
  public boolean isValidFxExperience() {
    return userInfoCorporate.validateFxExperience(false);
  }

  @JsonIgnore
  @AssertTrue(message = "投資経験（株式投資）を選択してください。")
  public boolean isValidStocksExperience() {
    return userInfoCorporate.validateStocksExperience(false);
  }

  @JsonIgnore
  @AssertTrue(message = "投資経験（投資信託）を選択してください。")
  public boolean isValidFundExperience() {
    return userInfoCorporate.validateFundExperience(false);
  }

  @JsonIgnore
  @AssertTrue(message = "申込経緯を選択してください。")
  public boolean isValidApplicationHistory() {
    return userInfoCorporate.validateApplicationHistory(false);
  }

  @JsonIgnore
  @AssertTrue(message = "申込経緯(その他)を入力してください。")
  public boolean isValidApplicationHistoryOther() {
    // 申込経緯（その他）が選択されている場合のみチェック
    if (userInfoCorporate.IsApplicationHistoryOther(false)) {
      return userInfoCorporate.validateApplicationHistoryOther(false);
    }
    return true;
  }

  @JsonIgnore
  @AssertTrue(message = "外国PEPsを選択してください。")
  public boolean isValidForeignPeps() {
    return userInfoCorporate.validateForeignPeps(false);
  }

  // 代表者

  @JsonIgnore
  @AssertTrue(message = "代表者名（名）を255文字以内で入力してください。")
  public boolean isValidRepresentativeFirstName() {
    return userInfoCorporate.getRepresentative().validateFirstName(false);
  }

  @JsonIgnore
  @AssertTrue(message = "代表者名（姓）を255文字以内で入力してください。")
  public boolean isValidRepresentativeLastName() {
    return userInfoCorporate.getRepresentative().validateLastName(false);
  }

  @JsonIgnore
  @AssertTrue(message = "代表者名カナ（名）を255文字以内で入力してください。")
  public boolean isValidRepresentativeFirstKana() {
    return userInfoCorporate.getRepresentative().validateFirstKana(false);
  }

  @JsonIgnore
  @AssertTrue(message = "代表者名カナ（姓）を255文字以内で入力してください。")
  public boolean isValidRepresentativeLastKana() {
    return userInfoCorporate.getRepresentative().validateLastKana(false);
  }

  @JsonIgnore
  @AssertTrue(message = "代表者の国籍を255文字以内で入力してください。")
  public boolean isValidRepresentativeNationality() {
    return userInfoCorporate.getRepresentative().validateNationality(false);
  }

  @JsonIgnore
  @AssertTrue(message = "代表者役職を255文字以内で入力してください。")
  public boolean isValidRepresentativePosition() {
    return userInfoCorporate.getRepresentative().validatePosition(false);
  }

  @JsonIgnore
  @AssertTrue(message = "代表者の郵便番号を7桁の数値で入力してください。")
  public boolean isValidRepresentativeZipCode() {
    return userInfoCorporate.getRepresentative().validateZipCode(false);
  }

  @JsonIgnore
  @AssertTrue(message = "代表者の都道府県を入力してください。")
  public boolean isValidRepresentativePrefecture() {
    return userInfoCorporate.getRepresentative().validatePrefecture(false);
  }

  @JsonIgnore
  @AssertTrue(message = "代表者の市区町村を255文字以内で入力してください。")
  public boolean isValidRepresentativeCity() {
    return userInfoCorporate.getRepresentative().validateCity(false);
  }

  @JsonIgnore
  @AssertTrue(message = "代表者の町名番地を255文字以内で入力してください。")
  public boolean isValidRepresentativeAddress() {
    return userInfoCorporate.getRepresentative().validateAddress(false);
  }

  @JsonIgnore
  @AssertTrue(message = "代表者の建物名を255文字以内で入力してください。")
  public boolean isValidRepresentativeBuilding() {
    return userInfoCorporate.getRepresentative().validateBuilding(true);
  }

  @JsonIgnore
  @AssertTrue(message = "代表者の国コードを選択してください")
  public boolean isValidRepresentativeCountry() {
    return userInfoCorporate.getRepresentative().validateCountry(false);
  }

  @JsonIgnore
  @AssertTrue(message = "代表者の携帯番号はハイフン無し10桁または11桁の数字で入力してください")
  public boolean isValidRepresentativePhonenNumber() {
    return userInfoCorporate.getRepresentative().validatePhoneNumber(false);
  }

  // 取引担当者

  @JsonIgnore
  @AssertTrue(message = "取引担当者の氏名（名）を255文字以内で入力してください。")
  public boolean isValidAgentFirstName() {
    if (userInfoCorporate.getAgent() == null) {
      return true;
    }

    return userInfoCorporate.getAgent().validateFirstName(false);
  }

  @JsonIgnore
  @AssertTrue(message = "取引担当者の氏名（姓）を255文字以内で入力してください。")
  public boolean isValidAgentLastName() {
    if (userInfoCorporate.getAgent() == null) {
      return true;
    }

    return userInfoCorporate.getAgent().validateLastName(false);
  }

  @JsonIgnore
  @AssertTrue(message = "取引担当者の氏名カナ（名）を255文字以内で入力してください。")
  public boolean isValidAgentFirstKana() {
    if (userInfoCorporate.getAgent() == null) {
      return true;
    }

    return userInfoCorporate.getAgent().validateFirstKana(false);
  }

  @JsonIgnore
  @AssertTrue(message = "取引担当者の氏名カナ（姓）を255文字以内で入力してください。")
  public boolean isValidAgentLastKana() {
    if (userInfoCorporate.getAgent() == null) {
      return true;
    }

    return userInfoCorporate.getAgent().validateLastKana(false);
  }

  @JsonIgnore
  @AssertTrue(message = "取引担当者の国籍を255文字以内で入力してください。")
  public boolean isValidAgnetNationality() {
    if (userInfoCorporate.getAgent() == null) {
      return true;
    }

    return userInfoCorporate.getAgent().validateNationality(false);
  }

  @JsonIgnore
  @AssertTrue(message = "取引担当者の役職を255文字以内で入力してください。")
  public boolean isValidAgnetPosition() {
    if (userInfoCorporate.getAgent() == null) {
      return true;
    }

    return userInfoCorporate.getAgent().validatePosition(false);
  }

  @JsonIgnore
  @AssertTrue(message = "取引担当者の郵便番号を7桁の数値で入力してください。")
  public boolean isValidAgnetZipCode() {
    if (userInfoCorporate.getAgent() == null) {
      return true;
    }

    return userInfoCorporate.getAgent().validateZipCode(false);
  }

  @JsonIgnore
  @AssertTrue(message = "取引担当者の都道府県を入力してください。")
  public boolean isValidAgnetPrefecture() {
    if (userInfoCorporate.getAgent() == null) {
      return true;
    }

    return userInfoCorporate.getAgent().validatePrefecture(false);
  }

  @JsonIgnore
  @AssertTrue(message = "取引担当者の市区町村を255文字以内で入力してください。")
  public boolean isValidAgnetCity() {
    if (userInfoCorporate.getAgent() == null) {
      return true;
    }

    return userInfoCorporate.getAgent().validateCity(false);
  }

  @JsonIgnore
  @AssertTrue(message = "取引担当者の町名番地を255文字以内で入力してください。")
  public boolean isValidAgnetAddress() {
    if (userInfoCorporate.getAgent() == null) {
      return true;
    }

    return userInfoCorporate.getAgent().validateAddress(false);
  }

  @JsonIgnore
  @AssertTrue(message = "取引担当者の建物名を255文字以内で入力してください。")
  public boolean isValidAgnetBuilding() {
    if (userInfoCorporate.getAgent() == null) {
      return true;
    }

    return userInfoCorporate.getAgent().validateBuilding(true);
  }

  @JsonIgnore
  @AssertTrue(message = "取引担当者の外国PEPsを選択してください。")
  public boolean isValidAgentForeignPeps() {
    if (userInfoCorporate.getAgent() == null) {
      return true;
    }
    return userInfoCorporate.getAgent().validateForeignPeps(false);
  }

  @JsonIgnore
  @AssertTrue(message = "取引担当者の国コードを選択してください")
  public boolean isValidAgentCountry() {
    if (userInfoCorporate.getAgent() == null) {
      return true;
    }

    return userInfoCorporate.getAgent().validateCountry(false);
  }

  @JsonIgnore
  @AssertTrue(message = "取引担当者の携帯番号はハイフン無し10桁または11桁の数字で入力してください")
  public boolean isValidAgentPhoneNumber() {
    if (userInfoCorporate.getAgent() == null) {
      return true;
    }

    return userInfoCorporate.getAgent().validatePhoneNumber(false);
  }

  // 実質的支配者

  @JsonIgnore
  @AssertTrue(message = "実質的支配者の氏名（名）を255文字以内で入力してください。")
  public boolean isValidOwnerFirstName() {
    if (userInfoCorporate.getOwners() == null) {
      return true;
    }

    for (Owner owner : userInfoCorporate.getOwners()) {
      if (!owner.validateFirstName(false)) {
        return false;
      }
    }

    return true;
  }

  @JsonIgnore
  @AssertTrue(message = "実質的支配者の氏名（姓）を255文字以内で入力してください。")
  public boolean isValidOwnerLastName() {
    if (userInfoCorporate.getOwners() == null) {
      return true;
    }

    for (Owner owner : userInfoCorporate.getOwners()) {
      if (!owner.validateLastName(false)) {
        return false;
      }
    }

    return true;
  }

  @JsonIgnore
  @AssertTrue(message = "実質的支配者の氏名カナ（名）を255文字以内で入力してください。")
  public boolean isValidOwnerFirstKana() {
    if (userInfoCorporate.getOwners() == null) {
      return true;
    }

    for (Owner owner : userInfoCorporate.getOwners()) {
      if (!owner.validateFirstKana(false)) {
        return false;
      }
    }

    return true;
  }

  @JsonIgnore
  @AssertTrue(message = "実質的支配者の氏名カナ（姓）を255文字以内で入力してください。")
  public boolean isValidOwnerLastKana() {
    if (userInfoCorporate.getOwners() == null) {
      return true;
    }

    for (Owner owner : userInfoCorporate.getOwners()) {
      if (!owner.validateLastKana(false)) {
        return false;
      }
    }

    return true;
  }

  @JsonIgnore
  @AssertTrue(message = "実質的支配者の国籍を255文字以内で入力してください。")
  public boolean isValidOwnerNationality() {
    if (userInfoCorporate.getOwners() == null) {
      return true;
    }

    for (Owner owner : userInfoCorporate.getOwners()) {
      if (!owner.validateNationality(false)) {
        return false;
      }
    }

    return true;
  }

  @JsonIgnore
  @AssertTrue(message = "実質的支配者の役職を255文字以内で入力してください。")
  public boolean isValidOwnerPosition() {
    if (userInfoCorporate.getOwners() == null) {
      return true;
    }

    for (Owner owner : userInfoCorporate.getOwners()) {
      if (!owner.validatePosition(false)) {
        return false;
      }
    }

    return true;
  }

  @JsonIgnore
  @AssertTrue(message = "実質的支配者の郵便番号を7桁の数値で入力してください。")
  public boolean isValidOwnerZipCode() {
    if (userInfoCorporate.getOwners() == null) {
      return true;
    }

    for (Owner owner : userInfoCorporate.getOwners()) {
      if (!owner.validateZipCode(false)) {
        return false;
      }
    }

    return true;
  }

  @JsonIgnore
  @AssertTrue(message = "実質的支配者の都道府県を入力してください。")
  public boolean isValidOwnerPrefecture() {
    if (userInfoCorporate.getOwners() == null) {
      return true;
    }

    for (Owner owner : userInfoCorporate.getOwners()) {
      if (!owner.validatePrefecture(false)) {
        return false;
      }
    }

    return true;
  }

  @JsonIgnore
  @AssertTrue(message = "実質的支配者の市区町村を255文字以内で入力してください。")
  public boolean isValidOwnerCity() {
    if (userInfoCorporate.getOwners() == null) {
      return true;
    }

    for (Owner owner : userInfoCorporate.getOwners()) {
      if (!owner.validateCity(false)) {
        return false;
      }
    }

    return true;
  }

  @JsonIgnore
  @AssertTrue(message = "実質的支配者の町名番地を255文字以内で入力してください。")
  public boolean isValidOwnerAddress() {
    if (userInfoCorporate.getOwners() == null) {
      return true;
    }

    for (Owner owner : userInfoCorporate.getOwners()) {
      if (!owner.validateAddress(false)) {
        return false;
      }
    }

    return true;
  }

  @JsonIgnore
  @AssertTrue(message = "実質的支配者の建物名を255文字以内で入力してください。")
  public boolean isValidOwnerBuilding() {
    if (userInfoCorporate.getOwners() == null) {
      return true;
    }

    for (Owner owner : userInfoCorporate.getOwners()) {
      if (!owner.validateBuilding(true)) {
        return false;
      }
    }

    return true;
  }

  @JsonIgnore
  @AssertTrue(message = "実質的支配者の外国PEPsを選択してください。")
  public boolean isValidOwnerForeignPeps() {
    if (userInfoCorporate.getOwners() == null) {
      return true;
    }

    for (Owner owner : userInfoCorporate.getOwners()) {
      if (!owner.validateForeignPeps(false)) {
        return false;
      }
    }

    return true;
  }
}
